# Free Fire Player Info API

A RESTful API service to retrieve Free Fire player information by UID (User ID). This API provides player statistics, rank information, and other game-related data.

## Features

- 🎮 Get player basic information by UID
- 📊 Retrieve detailed player statistics
- 🏆 Fetch player rank information
- 🌍 Multi-region support
- 🛡️ Rate limiting and security
- ✅ Input validation
- 📝 Comprehensive error handling
- 🚀 Fast and lightweight

## Quick Start

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd myapiforff
```

2. Install dependencies:
```bash
npm install
```

3. Create environment file:
```bash
cp .env.example .env
```

4. Configure your environment variables in `.env`

5. Start the server:
```bash
# Development mode
npm run dev

# Production mode
npm start
```

The API will be available at `http://localhost:3000`

## API Endpoints

### Base URL
```
http://localhost:3000
```

### Endpoints

#### 1. Get API Information
```http
GET /
```

Returns basic API information and available endpoints.

#### 2. Health Check
```http
GET /health
```

Returns server health status.

#### 3. Verify Player UID
```http
GET /api/player/{uid}/verify
```

**Parameters:**
- `uid` (path): Player UID (9-12 digits)
- `region` (query, optional): Player region (default: global)

**Example:**
```bash
curl http://localhost:3000/api/player/*********/verify?region=global
```

**Response:**
```json
{
  "success": true,
  "message": "Player UID verified successfully",
  "data": {
    "isValid": true,
    "exists": true,
    "playerName": "PlayerName",
    "level": 45,
    "region": "SG"
  },
  "timestamp": "2024-01-15T12:00:00.000Z"
}
```

#### 4. Get Player Information
```http
GET /api/player/{uid}
```

**Parameters:**
- `uid` (path): Player UID (9-12 digits)
- `region` (query, optional): Player region (default: global)

**Example:**
```bash
curl http://localhost:3000/api/player/*********?region=global
```

**Response:**
```json
{
  "success": true,
  "message": "Player information retrieved successfully",
  "data": {
    "uid": "*********",
    "region": "global",
    "nickname": "Player_*********",
    "level": 45,
    "avatar": null,
    "guild": "Guild_123",
    "lastSeen": "2024-01-15T10:30:00.000Z",
    "accountCreated": null,
    "isOnline": false,
    "signature": "Pro player!",
    "badges": [],
    "fetchedAt": "2024-01-15T12:00:00.000Z"
  },
  "timestamp": "2024-01-15T12:00:00.000Z"
}
```

#### 5. Get Player Statistics
```http
GET /api/player/{uid}/stats
```

**Parameters:**
- `uid` (path): Player UID (9-12 digits)
- `region` (query, optional): Player region (default: global)

**Example:**
```bash
curl http://localhost:3000/api/player/*********/stats
```

**Response:**
```json
{
  "success": true,
  "message": "Player statistics retrieved successfully",
  "data": {
    "uid": "*********",
    "region": "global",
    "stats": {
      "classicMode": {
        "matches": 500,
        "wins": 125,
        "kills": 2500,
        "deaths": 1800,
        "kd": "1.39",
        "winRate": "25.0%",
        "headshots": 750,
        "headshotRate": "30.0%"
      },
      "rankedMode": {
        "currentRank": "Gold",
        "rankPoints": 2500,
        "matches": 200,
        "wins": 60,
        "kills": 1200,
        "kd": "1.85"
      },
      "clashSquad": {
        "matches": 150,
        "wins": 45,
        "kills": 800,
        "kd": "2.10"
      }
    },
    "achievements": [],
    "favoriteWeapons": ["AK47", "M4A1", "AWM"],
    "fetchedAt": "2024-01-15T12:00:00.000Z"
  },
  "timestamp": "2024-01-15T12:00:00.000Z"
}
```

#### 6. Get Player Rank
```http
GET /api/player/{uid}/rank
```

**Parameters:**
- `uid` (path): Player UID (9-12 digits)
- `region` (query, optional): Player region (default: global)
- `season` (query, optional): Season (default: current)

**Example:**
```bash
curl http://localhost:3000/api/player/*********/rank?season=current
```

## Supported Regions

- `global` (default)
- `india`
- `brazil`
- `indonesia`
- `thailand`
- `vietnam`
- `middle-east`

## Error Handling

The API returns standardized error responses:

```json
{
  "success": false,
  "error": true,
  "message": "Error description",
  "statusCode": 400,
  "timestamp": "2024-01-15T12:00:00.000Z"
}
```

### Common Error Codes

- `400` - Bad Request (Invalid UID format, validation errors)
- `404` - Not Found (Player not found)
- `429` - Too Many Requests (Rate limit exceeded)
- `500` - Internal Server Error

## Rate Limiting

- **Window:** 15 minutes
- **Limit:** 100 requests per IP
- **Headers:** Rate limit information is included in response headers

## Environment Variables

Copy `.env.example` to `.env` and configure:

```env
PORT=3000
NODE_ENV=development
FREEFIRE_API_KEY=your_api_key_here
RATE_LIMIT_MAX=100
# ... see .env.example for all options
```

## Development

### Project Structure

```
myapiforff/
├── config/
│   └── config.js          # Configuration settings
├── middleware/
│   ├── errorHandler.js    # Error handling middleware
│   └── validation.js      # Input validation
├── routes/
│   └── player.js          # Player routes
├── services/
│   └── freeFireService.js # Free Fire data service
├── utils/
│   └── responseHelper.js  # Response utilities
├── server.js              # Main server file
├── package.json
├── .env.example
├── .gitignore
└── README.md
```

### Scripts

```bash
npm start       # Start production server
npm run dev     # Start development server with nodemon
npm test        # Run tests (not implemented yet)
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

ISC License

## Disclaimer

This API is for educational and development purposes. Free Fire is a trademark of Garena. This project is not affiliated with or endorsed by Garena.

## Support

For issues and questions, please open an issue on the repository.
