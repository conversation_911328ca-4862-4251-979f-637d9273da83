/**
 * Standardized success response
 * @param {Object} res - Express response object
 * @param {*} data - Response data
 * @param {string} message - Success message
 * @param {number} statusCode - HTTP status code (default: 200)
 * @returns {Object} JSON response
 */
const successResponse = (res, data, message = 'Success', statusCode = 200) => {
  const response = {
    success: true,
    message: message,
    data: data,
    timestamp: new Date().toISOString()
  };

  // Add pagination info if data has pagination
  if (data && typeof data === 'object' && data.pagination) {
    response.pagination = data.pagination;
    response.data = data.results || data.data;
  }

  return res.status(statusCode).json(response);
};

/**
 * Standardized error response
 * @param {Object} res - Express response object
 * @param {string} message - Error message
 * @param {number} statusCode - HTTP status code (default: 400)
 * @param {*} details - Additional error details
 * @returns {Object} JSON response
 */
const errorResponse = (res, message = 'An error occurred', statusCode = 400, details = null) => {
  const response = {
    success: false,
    error: true,
    message: message,
    statusCode: statusCode,
    timestamp: new Date().toISOString()
  };

  // Add details if provided and not in production
  if (details && process.env.NODE_ENV !== 'production') {
    response.details = details;
  }

  return res.status(statusCode).json(response);
};

/**
 * Paginated response helper
 * @param {Object} res - Express response object
 * @param {Array} data - Array of data items
 * @param {number} page - Current page number
 * @param {number} limit - Items per page
 * @param {number} total - Total number of items
 * @param {string} message - Success message
 * @returns {Object} JSON response
 */
const paginatedResponse = (res, data, page, limit, total, message = 'Data retrieved successfully') => {
  const totalPages = Math.ceil(total / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  const response = {
    success: true,
    message: message,
    data: data,
    pagination: {
      currentPage: page,
      totalPages: totalPages,
      totalItems: total,
      itemsPerPage: limit,
      hasNextPage: hasNextPage,
      hasPrevPage: hasPrevPage,
      nextPage: hasNextPage ? page + 1 : null,
      prevPage: hasPrevPage ? page - 1 : null
    },
    timestamp: new Date().toISOString()
  };

  return res.status(200).json(response);
};

/**
 * No content response (204)
 * @param {Object} res - Express response object
 * @returns {Object} Empty response
 */
const noContentResponse = (res) => {
  return res.status(204).send();
};

/**
 * Created response (201)
 * @param {Object} res - Express response object
 * @param {*} data - Created resource data
 * @param {string} message - Success message
 * @returns {Object} JSON response
 */
const createdResponse = (res, data, message = 'Resource created successfully') => {
  return successResponse(res, data, message, 201);
};

/**
 * Accepted response (202)
 * @param {Object} res - Express response object
 * @param {*} data - Response data
 * @param {string} message - Success message
 * @returns {Object} JSON response
 */
const acceptedResponse = (res, data, message = 'Request accepted') => {
  return successResponse(res, data, message, 202);
};

/**
 * Not found response (404)
 * @param {Object} res - Express response object
 * @param {string} message - Error message
 * @returns {Object} JSON response
 */
const notFoundResponse = (res, message = 'Resource not found') => {
  return errorResponse(res, message, 404);
};

/**
 * Unauthorized response (401)
 * @param {Object} res - Express response object
 * @param {string} message - Error message
 * @returns {Object} JSON response
 */
const unauthorizedResponse = (res, message = 'Unauthorized access') => {
  return errorResponse(res, message, 401);
};

/**
 * Forbidden response (403)
 * @param {Object} res - Express response object
 * @param {string} message - Error message
 * @returns {Object} JSON response
 */
const forbiddenResponse = (res, message = 'Access forbidden') => {
  return errorResponse(res, message, 403);
};

/**
 * Validation error response (422)
 * @param {Object} res - Express response object
 * @param {*} errors - Validation errors
 * @param {string} message - Error message
 * @returns {Object} JSON response
 */
const validationErrorResponse = (res, errors, message = 'Validation failed') => {
  return errorResponse(res, message, 422, errors);
};

module.exports = {
  successResponse,
  errorResponse,
  paginatedResponse,
  noContentResponse,
  createdResponse,
  acceptedResponse,
  notFoundResponse,
  unauthorizedResponse,
  forbiddenResponse,
  validationErrorResponse
};
