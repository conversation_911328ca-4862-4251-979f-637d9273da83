# Server Configuration
PORT=3000
HOST=localhost
NODE_ENV=development

# API Configuration
API_TIMEOUT=10000
RETRY_ATTEMPTS=3
RETRY_DELAY=1000

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# CORS Configuration
CORS_ORIGIN=*
CORS_CREDENTIALS=false

# Free Fire API Configuration
FREEFIRE_BASE_URL=https://ff.garena.com
FREEFIRE_API_KEY=your_api_key_here
FREEFIRE_TIMEOUT=10000
FREEFIRE_RETRY_ATTEMPTS=3
FREEFIRE_RETRY_DELAY=1000
FREEFIRE_DEFAULT_REGION=global

# Cache Configuration
CACHE_ENABLED=false
CACHE_TTL=300
CACHE_MAX_KEYS=1000

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=combined
LOG_FILE=

# Database Configuration (Optional)
DATABASE_ENABLED=false
DATABASE_URL=

# External APIs (Optional)
HL_GAMING_API_URL=
HL_GAMING_API_KEY=
FREE_FIRE_API_URL=
FREE_FIRE_API_KEY=
