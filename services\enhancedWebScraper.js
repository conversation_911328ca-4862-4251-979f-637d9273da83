const axios = require('axios');
const cheerio = require('cheerio');

class EnhancedWebScraper {
  constructor() {
    this.baseURL = 'https://ff.garena.com';
    this.timeout = 20000;
    this.maxRetries = 3;
    
    // Enhanced headers to avoid detection
    this.headers = {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.9',
      'Accept-Encoding': 'gzip, deflate, br',
      'DNT': '1',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1',
      'Sec-Fetch-Dest': 'document',
      'Sec-Fetch-Mode': 'navigate',
      'Sec-Fetch-Site': 'none',
      'Cache-Control': 'max-age=0',
      'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"Windows"'
    };

    // Cache for scraped data
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Enhanced account verification using multiple methods
   * @param {string} uid - Player UID
   * @param {string} region - Region
   * @returns {Object} Verification result
   */
  async verifyAccountAdvanced(uid, region = 'global') {
    try {
      console.log(`🔍 Enhanced verification for UID: ${uid}`);
      
      // Check cache first
      const cacheKey = `verify_${uid}_${region}`;
      if (this.cache.has(cacheKey)) {
        const cached = this.cache.get(cacheKey);
        if (Date.now() - cached.timestamp < this.cacheTimeout) {
          console.log('✅ Using cached verification result');
          return cached.data;
        }
      }

      const results = {
        uid: uid,
        region: region,
        isValid: this.isValidUID(uid),
        exists: false,
        confidence: 0,
        methods: [],
        data: {},
        timestamp: new Date().toISOString()
      };

      // Method 1: Support page account check
      const supportCheck = await this.checkSupportPage(uid, region);
      if (supportCheck.success) {
        results.exists = true;
        results.confidence += 30;
        results.methods.push('support_page');
        results.data.supportCheck = supportCheck.data;
      }

      // Method 2: Try to find UID in public pages
      const publicSearch = await this.searchPublicPages(uid, region);
      if (publicSearch.found) {
        results.exists = true;
        results.confidence += 40;
        results.methods.push('public_search');
        results.data.publicData = publicSearch.data;
      }

      // Method 3: Pattern analysis
      const patternAnalysis = await this.analyzeUIDPattern(uid);
      if (patternAnalysis.valid) {
        results.confidence += 20;
        results.methods.push('pattern_analysis');
        results.data.pattern = patternAnalysis;
      }

      // Method 4: Region-specific checks
      const regionCheck = await this.checkRegionSpecific(uid, region);
      if (regionCheck.valid) {
        results.confidence += 10;
        results.methods.push('region_check');
        results.data.regionData = regionCheck.data;
      }

      // Determine final existence based on confidence
      if (results.confidence >= 50) {
        results.exists = true;
      } else if (results.confidence >= 20 && results.isValid) {
        results.exists = true; // Likely exists based on valid format
      }

      // Cache the result
      this.cache.set(cacheKey, {
        data: results,
        timestamp: Date.now()
      });

      return results;

    } catch (error) {
      console.error('Enhanced verification failed:', error.message);
      return {
        uid: uid,
        region: region,
        isValid: this.isValidUID(uid),
        exists: false,
        confidence: 0,
        methods: ['error'],
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Check Free Fire support page for account
   * @param {string} uid - Player UID
   * @param {string} region - Region
   * @returns {Object} Support check result
   */
  async checkSupportPage(uid, region) {
    try {
      const regionCode = this.getRegionCode(region);
      const supportURL = `${this.baseURL}/${regionCode}/support/`;
      
      console.log(`📋 Checking support page: ${supportURL}`);
      
      const response = await axios.get(supportURL, {
        headers: this.headers,
        timeout: this.timeout
      });

      const $ = cheerio.load(response.data);
      
      // Look for account check form
      const accountInput = $('input[placeholder*="account"], input[placeholder*="ID"]').first();
      
      if (accountInput.length > 0) {
        // Try to simulate form submission
        const formData = await this.simulateFormSubmission($, uid);
        
        return {
          success: true,
          data: {
            formFound: true,
            inputField: accountInput.attr('placeholder') || 'Account ID',
            formAction: formData.action,
            method: 'support_page_form'
          }
        };
      }

      return { success: false, reason: 'No account check form found' };

    } catch (error) {
      console.error('Support page check failed:', error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * Search for UID in public Free Fire pages
   * @param {string} uid - Player UID
   * @param {string} region - Region
   * @returns {Object} Search result
   */
  async searchPublicPages(uid, region) {
    try {
      console.log(`🔍 Searching public pages for UID: ${uid}`);
      
      const searchTargets = [
        '/en/news/',
        '/en/video/',
        '/en/chars/',
        '/universe/en/'
      ];

      for (const target of searchTargets) {
        try {
          const url = `${this.baseURL}${target}`;
          const response = await axios.get(url, {
            headers: this.headers,
            timeout: this.timeout
          });

          // Search for UID in page content
          if (response.data.includes(uid)) {
            console.log(`✅ Found UID in: ${url}`);
            return {
              found: true,
              data: {
                foundIn: url,
                context: 'public_page_content'
              }
            };
          }
        } catch (error) {
          // Continue searching other pages
          continue;
        }
      }

      return { found: false };

    } catch (error) {
      console.error('Public page search failed:', error.message);
      return { found: false, error: error.message };
    }
  }

  /**
   * Analyze UID pattern for validity indicators
   * @param {string} uid - Player UID
   * @returns {Object} Pattern analysis
   */
  async analyzeUIDPattern(uid) {
    try {
      console.log(`🔢 Analyzing UID pattern: ${uid}`);
      
      const analysis = {
        valid: false,
        length: uid.length,
        isNumeric: /^\d+$/.test(uid),
        patterns: [],
        confidence: 0
      };

      // Check length (Free Fire UIDs are typically 9-12 digits)
      if (uid.length >= 9 && uid.length <= 12) {
        analysis.patterns.push('valid_length');
        analysis.confidence += 25;
      }

      // Check if purely numeric
      if (analysis.isNumeric) {
        analysis.patterns.push('numeric_only');
        analysis.confidence += 25;
      }

      // Check for common Free Fire UID patterns
      const firstDigit = uid.charAt(0);
      if (['1', '2', '3', '4', '5'].includes(firstDigit)) {
        analysis.patterns.push('common_start_digit');
        analysis.confidence += 20;
      }

      // Check for sequential patterns (less likely to be real)
      if (this.hasSequentialPattern(uid)) {
        analysis.patterns.push('sequential_pattern');
        analysis.confidence -= 30; // Reduce confidence for obvious patterns
      }

      // Check for repeated digits (less likely to be real)
      if (this.hasRepeatedDigits(uid)) {
        analysis.patterns.push('repeated_digits');
        analysis.confidence -= 20;
      }

      analysis.valid = analysis.confidence >= 30;

      return analysis;

    } catch (error) {
      console.error('Pattern analysis failed:', error.message);
      return { valid: false, error: error.message };
    }
  }

  /**
   * Check region-specific indicators
   * @param {string} uid - Player UID
   * @param {string} region - Region
   * @returns {Object} Region check result
   */
  async checkRegionSpecific(uid, region) {
    try {
      console.log(`🌍 Checking region-specific data for: ${region}`);
      
      // Different regions might have different UID patterns or ranges
      const regionPatterns = {
        'india': { startDigits: ['1', '2', '3'], commonLength: 10 },
        'singapore': { startDigits: ['1', '3', '4'], commonLength: 10 },
        'brazil': { startDigits: ['2', '4', '5'], commonLength: 10 },
        'global': { startDigits: ['1', '2', '3', '4', '5'], commonLength: 10 }
      };

      const pattern = regionPatterns[region.toLowerCase()] || regionPatterns['global'];
      
      const analysis = {
        valid: false,
        matchesRegionPattern: false,
        data: {
          expectedLength: pattern.commonLength,
          actualLength: uid.length,
          expectedStartDigits: pattern.startDigits,
          actualStartDigit: uid.charAt(0)
        }
      };

      // Check if UID matches region pattern
      if (pattern.startDigits.includes(uid.charAt(0)) && uid.length === pattern.commonLength) {
        analysis.matchesRegionPattern = true;
        analysis.valid = true;
      }

      return analysis;

    } catch (error) {
      console.error('Region check failed:', error.message);
      return { valid: false, error: error.message };
    }
  }

  /**
   * Simulate form submission for account check
   * @param {Object} $ - Cheerio instance
   * @param {string} uid - Player UID
   * @returns {Object} Form data
   */
  async simulateFormSubmission($, uid) {
    try {
      const form = $('form').first();
      const action = form.attr('action') || '/account-check';
      const method = form.attr('method') || 'POST';
      
      return {
        action: action,
        method: method,
        uid: uid,
        simulated: true
      };
    } catch (error) {
      return { error: error.message };
    }
  }

  /**
   * Check for sequential patterns in UID
   * @param {string} uid - Player UID
   * @returns {boolean} Has sequential pattern
   */
  hasSequentialPattern(uid) {
    for (let i = 0; i < uid.length - 2; i++) {
      const a = parseInt(uid[i]);
      const b = parseInt(uid[i + 1]);
      const c = parseInt(uid[i + 2]);
      
      if (b === a + 1 && c === b + 1) {
        return true; // Found ascending sequence
      }
      if (b === a - 1 && c === b - 1) {
        return true; // Found descending sequence
      }
    }
    return false;
  }

  /**
   * Check for repeated digits in UID
   * @param {string} uid - Player UID
   * @returns {boolean} Has repeated digits
   */
  hasRepeatedDigits(uid) {
    const digitCounts = {};
    for (const digit of uid) {
      digitCounts[digit] = (digitCounts[digit] || 0) + 1;
      if (digitCounts[digit] >= 4) {
        return true; // 4 or more of the same digit
      }
    }
    return false;
  }

  /**
   * Get region code for URL
   * @param {string} region - Region name
   * @returns {string} Region code
   */
  getRegionCode(region) {
    const mapping = {
      'global': 'en',
      'india': 'en',
      'brazil': 'pt',
      'singapore': 'en',
      'russia': 'ru',
      'indonesia': 'id',
      'taiwan': 'zh',
      'usa': 'en',
      'vietnam': 'vn',
      'thailand': 'th',
      'middle-east': 'ME_en',
      'pakistan': 'en-pk'
    };
    return mapping[region.toLowerCase()] || 'en';
  }

  /**
   * Validate UID format
   * @param {string} uid - Player UID
   * @returns {boolean} Is valid format
   */
  isValidUID(uid) {
    return /^\d{9,12}$/.test(uid);
  }

  /**
   * Clear cache
   */
  clearCache() {
    this.cache.clear();
  }
}

module.exports = new EnhancedWebScraper();
