const axios = require('axios');

async function testFreeAPIs() {
  console.log('🔍 Testing Free Fire APIs with UID: ********** (India Region)');
  console.log('=' .repeat(70));

  const uid = '**********';
  const testUIDs = ['**********', '**********', '**********']; // Multiple UIDs to test

  // Free APIs to test
  const freeAPIs = [
    {
      name: 'Jinix6 Free FF API',
      url: 'https://free-ff-api-src-5plp.onrender.com/api/v1/account',
      params: { region: 'IND', uid: uid },
      method: 'GET',
      free: true,
      features: ['Account Info', 'Player Stats', 'Guild Info', 'Wishlist']
    },
    {
      name: 'Theekshana Public API',
      url: `https://www.public.freefireinfo.site/api/info/ind/${uid}`,
      params: { key: 'astute_ff' },
      method: 'GET',
      free: true,
      features: ['Account Info', 'Guild Info', 'Equipment', 'Pet Info'],
      note: 'Requires Telegram key from @FreeFireInfoSite'
    },
    {
      name: 'Alternative Public API',
      url: `https://freefireinfo.site/api/info/ind/${uid}`,
      params: { key: 'astute_ff' },
      method: 'GET',
      free: true,
      features: ['Account Info', 'Guild Info', 'Equipment']
    }
  ];

  for (const api of freeAPIs) {
    console.log(`\n🧪 Testing: ${api.name}`);
    console.log(`📡 URL: ${api.url}`);
    console.log(`🆓 Free: ${api.free ? 'YES' : 'NO'}`);
    console.log(`⚡ Features: ${api.features.join(', ')}`);
    if (api.note) console.log(`📝 Note: ${api.note}`);
    
    try {
      let response;
      
      if (api.method === 'POST') {
        response = await axios.post(api.url, api.params, {
          timeout: 15000,
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
          }
        });
      } else {
        response = await axios.get(api.url, {
          params: api.params,
          timeout: 15000,
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
          }
        });
      }

      console.log(`✅ Status: ${response.status}`);
      console.log(`📊 Response Type: ${typeof response.data}`);
      
      if (typeof response.data === 'object' && response.data !== null) {
        const keys = Object.keys(response.data);
        console.log(`🎯 Response Keys: ${keys.slice(0, 5).join(', ')}${keys.length > 5 ? '...' : ''}`);
        
        // Check for player data indicators
        if (response.data.basicInfo || response.data['Account Name'] || response.data.nickname) {
          console.log(`🎮 ✅ REAL PLAYER DATA FOUND!`);
          
          if (response.data.basicInfo) {
            // Jinix6 API format
            console.log(`   • Nickname: ${response.data.basicInfo.nickname || 'N/A'}`);
            console.log(`   • Level: ${response.data.basicInfo.level || 'N/A'}`);
            console.log(`   • UID: ${response.data.basicInfo.accountId || uid}`);
            console.log(`   • Region: ${response.data.basicInfo.region || 'N/A'}`);
            console.log(`   • Likes: ${response.data.basicInfo.liked || 'N/A'}`);
            if (response.data.clanBasicInfo) {
              console.log(`   • Guild: ${response.data.clanBasicInfo.clanName || 'None'}`);
            }
          } else if (response.data['Account Name']) {
            // Theekshana API format
            console.log(`   • Nickname: ${response.data['Account Name']}`);
            console.log(`   • Level: ${response.data['Account Level'] || 'N/A'}`);
            console.log(`   • UID: ${response.data['Account UID'] || uid}`);
            console.log(`   • Region: ${response.data['Account Region'] || 'N/A'}`);
            console.log(`   • Likes: ${response.data['Account Likes'] || 'N/A'}`);
            if (response.data['Guild Information']) {
              console.log(`   • Guild: ${response.data['Guild Information']['Guild Name'] || 'None'}`);
            }
          }
          
          console.log(`🚀 API STATUS: WORKING WITH REAL DATA!`);
        } else if (response.data.error) {
          console.log(`❌ API Error: ${response.data.error}`);
        } else {
          console.log(`📄 Sample Data: ${JSON.stringify(response.data).substring(0, 150)}...`);
        }
      } else if (typeof response.data === 'string') {
        if (response.data.includes('<!doctype html>') || response.data.includes('<html')) {
          console.log(`🌐 Response: HTML page (API down or parking page)`);
        } else {
          console.log(`📝 Response: ${response.data.substring(0, 150)}...`);
        }
      }

    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
      if (error.response) {
        console.log(`   • Status: ${error.response.status}`);
        console.log(`   • Status Text: ${error.response.statusText}`);
        if (error.response.data && typeof error.response.data === 'object') {
          console.log(`   • Error Details: ${JSON.stringify(error.response.data).substring(0, 100)}...`);
        }
      }
    }
  }

  console.log('\n' + '=' .repeat(70));
  console.log('📋 FREE API SUMMARY:');
  console.log('');
  console.log('🎯 BEST FREE OPTIONS:');
  console.log('1. **Jinix6 Free FF API** (https://free-ff-api-src-5plp.onrender.com)');
  console.log('   ✅ Completely free, no API key required');
  console.log('   ✅ Multiple endpoints (account, stats, guild, wishlist)');
  console.log('   ✅ Well-documented on GitHub');
  console.log('   ❓ Status: Need to check if currently working');
  console.log('');
  console.log('2. **Theekshana Public API** (https://www.public.freefireinfo.site)');
  console.log('   ✅ Free with Telegram key');
  console.log('   ✅ Detailed player information');
  console.log('   ✅ Guild and equipment data');
  console.log('   📝 Requires joining Telegram: @FreeFireInfoSite');
  console.log('');
  console.log('💡 INTEGRATION RECOMMENDATIONS:');
  console.log('• Test both APIs with multiple UIDs');
  console.log('• Implement fallback between APIs');
  console.log('• Cache successful responses');
  console.log('• Handle rate limiting gracefully');
  console.log('• Monitor API status regularly');
  console.log('');
  console.log('🔧 NEXT STEPS:');
  console.log('1. Get Telegram key from @FreeFireInfoSite');
  console.log('2. Test APIs with known working UIDs');
  console.log('3. Integrate working API into your service');
  console.log('4. Add proper error handling and fallbacks');
}

// Run the test
testFreeAPIs().catch(console.error);
