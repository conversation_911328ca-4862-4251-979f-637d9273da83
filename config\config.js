require('dotenv').config();

const config = {
  // Server configuration
  server: {
    port: process.env.PORT || 3000,
    host: process.env.HOST || 'localhost',
    environment: process.env.NODE_ENV || 'development'
  },

  // API configuration
  api: {
    version: '1.0.0',
    prefix: '/api',
    timeout: parseInt(process.env.API_TIMEOUT) || 10000,
    retryAttempts: parseInt(process.env.RETRY_ATTEMPTS) || 3,
    retryDelay: parseInt(process.env.RETRY_DELAY) || 1000
  },

  // Rate limiting configuration
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW) || 15 * 60 * 1000, // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_MAX) || 100, // limit each IP to 100 requests per windowMs
    message: {
      error: 'Too many requests from this IP, please try again later.',
      retryAfter: '15 minutes'
    }
  },

  // CORS configuration
  cors: {
    origin: process.env.CORS_ORIGIN || '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    credentials: process.env.CORS_CREDENTIALS === 'true' || false
  },

  // Free Fire API configuration
  freefire: {
    baseURL: process.env.FREEFIRE_BASE_URL || 'https://ff.garena.com',
    apiKey: process.env.FREEFIRE_API_KEY || null,
    timeout: parseInt(process.env.FREEFIRE_TIMEOUT) || 10000,
    retryAttempts: parseInt(process.env.FREEFIRE_RETRY_ATTEMPTS) || 3,
    retryDelay: parseInt(process.env.FREEFIRE_RETRY_DELAY) || 1000,
    regions: {
      global: 'global',
      india: 'india',
      brazil: 'brazil',
      indonesia: 'indonesia',
      thailand: 'thailand',
      vietnam: 'vietnam',
      'middle-east': 'middle-east'
    },
    defaultRegion: process.env.FREEFIRE_DEFAULT_REGION || 'global'
  },

  // Cache configuration (for future implementation)
  cache: {
    enabled: process.env.CACHE_ENABLED === 'true' || false,
    ttl: parseInt(process.env.CACHE_TTL) || 300, // 5 minutes
    maxKeys: parseInt(process.env.CACHE_MAX_KEYS) || 1000
  },

  // Logging configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: process.env.LOG_FORMAT || 'combined',
    file: process.env.LOG_FILE || null
  },

  // Security configuration
  security: {
    helmet: {
      contentSecurityPolicy: process.env.NODE_ENV === 'production',
      crossOriginEmbedderPolicy: false
    }
  },

  // Database configuration (for future implementation)
  database: {
    enabled: process.env.DATABASE_ENABLED === 'true' || false,
    url: process.env.DATABASE_URL || null,
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true
    }
  },

  // External APIs configuration
  external: {
    // Third-party Free Fire APIs
    hlGaming: {
      baseURL: process.env.HL_GAMING_API_URL || null,
      apiKey: process.env.HL_GAMING_API_KEY || null
    },
    freeFireAPI: {
      baseURL: process.env.FREE_FIRE_API_URL || null,
      apiKey: process.env.FREE_FIRE_API_KEY || null
    }
  }
};

// Validation
const validateConfig = () => {
  const errors = [];

  // Check required environment variables
  if (config.server.environment === 'production') {
    if (!config.freefire.apiKey && !config.external.hlGaming.apiKey) {
      errors.push('API key is required in production environment');
    }
  }

  // Validate port
  if (isNaN(config.server.port) || config.server.port < 1 || config.server.port > 65535) {
    errors.push('Invalid port number');
  }

  // Validate rate limit settings
  if (config.rateLimit.max < 1) {
    errors.push('Rate limit max must be greater than 0');
  }

  if (errors.length > 0) {
    throw new Error(`Configuration validation failed:\n${errors.join('\n')}`);
  }
};

// Validate configuration on load
try {
  validateConfig();
} catch (error) {
  console.error('Configuration Error:', error.message);
  process.exit(1);
}

module.exports = config;
