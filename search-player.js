const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function searchPlayer(uid, region = 'india') {
  console.log(`🔍 Searching for Free Fire Player: ${uid} (Region: ${region})`);
  console.log('=' .repeat(60));

  try {
    // 1. Verify UID
    console.log('\n1️⃣ VERIFYING PLAYER UID...');
    const verification = await axios.get(`${BASE_URL}/api/player/${uid}/verify?region=${region}`);
    
    if (verification.data.success) {
      const verifyData = verification.data.data;
      console.log('✅ UID Verification: SUCCESS');
      console.log(`   • Valid: ${verifyData.isValid}`);
      console.log(`   • Exists: ${verifyData.exists}`);
      console.log(`   • Player Name: ${verifyData.playerName}`);
      console.log(`   • Level: ${verifyData.level || 'Unknown'}`);
      console.log(`   • Region: ${verifyData.region}`);
      if (verifyData.note) console.log(`   • Note: ${verifyData.note}`);
    } else {
      console.log('❌ UID Verification: FAILED');
      console.log(`   • Error: ${verification.data.message}`);
      return;
    }

    // 2. Get Player Information
    console.log('\n2️⃣ GETTING PLAYER INFORMATION...');
    const playerInfo = await axios.get(`${BASE_URL}/api/player/${uid}?region=${region}`);
    
    if (playerInfo.data.success) {
      const player = playerInfo.data.data;
      console.log('✅ Player Information: SUCCESS');
      console.log(`   • UID: ${player.uid}`);
      console.log(`   • Nickname: ${player.nickname}`);
      console.log(`   • Level: ${player.level}`);
      console.log(`   • Region: ${player.region}`);
      console.log(`   • Verified: ${player.verified}`);
      console.log(`   • Data Source: ${player.dataSource}`);
      console.log(`   • Avatar: ${player.avatar || 'None'}`);
      console.log(`   • Guild: ${player.guild || 'None'}`);
      console.log(`   • Last Seen: ${player.lastSeen || 'Unknown'}`);
      console.log(`   • Signature: ${player.signature || 'None'}`);
      console.log(`   • Badges: ${player.badges || 0}`);
    }

    // 3. Get Player Statistics
    console.log('\n3️⃣ GETTING PLAYER STATISTICS...');
    const playerStats = await axios.get(`${BASE_URL}/api/player/${uid}/stats?region=${region}`);
    
    if (playerStats.data.success) {
      const stats = playerStats.data.data;
      console.log('✅ Player Statistics: SUCCESS');
      console.log(`   • Verified: ${stats.verified}`);
      console.log(`   • Data Source: ${stats.dataSource}`);
      
      if (stats.stats.classicMode.solo) {
        // Real API data structure
        console.log('\n   📊 SOLO MODE:');
        console.log(`      • Matches: ${stats.stats.classicMode.solo.matches}`);
        console.log(`      • Wins: ${stats.stats.classicMode.solo.wins}`);
        console.log(`      • Kills: ${stats.stats.classicMode.solo.kills}`);
        console.log(`      • Deaths: ${stats.stats.classicMode.solo.deaths}`);
        console.log(`      • K/D Ratio: ${stats.stats.classicMode.solo.kd}`);
        console.log(`      • Win Rate: ${stats.stats.classicMode.solo.winRate}`);
        console.log(`      • Headshots: ${stats.stats.classicMode.solo.headshots}`);
        
        console.log('\n   📊 SQUAD MODE:');
        console.log(`      • Matches: ${stats.stats.classicMode.squad.matches}`);
        console.log(`      • Wins: ${stats.stats.classicMode.squad.wins}`);
        console.log(`      • Kills: ${stats.stats.classicMode.squad.kills}`);
        console.log(`      • K/D Ratio: ${stats.stats.classicMode.squad.kd}`);
        console.log(`      • Revives: ${stats.stats.classicMode.squad.revives}`);
      } else {
        // Fallback data structure
        console.log('\n   📊 CLASSIC MODE:');
        console.log(`      • Matches: ${stats.stats.classicMode.matches}`);
        console.log(`      • Wins: ${stats.stats.classicMode.wins}`);
        console.log(`      • Kills: ${stats.stats.classicMode.kills}`);
        console.log(`      • Deaths: ${stats.stats.classicMode.deaths}`);
        console.log(`      • K/D Ratio: ${stats.stats.classicMode.kd}`);
        console.log(`      • Win Rate: ${stats.stats.classicMode.winRate}`);
        console.log(`      • Headshots: ${stats.stats.classicMode.headshots}`);
      }
    }

    // 4. Get Player Rank
    console.log('\n4️⃣ GETTING PLAYER RANK...');
    const playerRank = await axios.get(`${BASE_URL}/api/player/${uid}/rank?region=${region}`);
    
    if (playerRank.data.success) {
      const rank = playerRank.data.data;
      console.log('✅ Player Rank: SUCCESS');
      console.log(`   • Current Rank: ${rank.currentRank}`);
      console.log(`   • Rank Points: ${rank.rankPoints}`);
      console.log(`   • Tier: ${rank.tier}`);
      console.log(`   • Max Rank: ${rank.maxRank}`);
      console.log(`   • Season: ${rank.season}`);
      
      if (rank.seasonStats) {
        console.log('\n   🏆 SEASON STATS:');
        console.log(`      • Matches: ${rank.seasonStats.matches}`);
        console.log(`      • Wins: ${rank.seasonStats.wins}`);
        console.log(`      • Kills: ${rank.seasonStats.kills}`);
        console.log(`      • K/D: ${rank.seasonStats.kd}`);
      }
    }

    console.log('\n' + '=' .repeat(60));
    console.log('🎉 SEARCH COMPLETED SUCCESSFULLY!');
    
  } catch (error) {
    console.error('\n❌ ERROR:', error.response?.data?.message || error.message);
    console.error('Status Code:', error.response?.status || 'Unknown');
  }
}

// Get UID and region from command line arguments
const uid = process.argv[2];
const region = process.argv[3] || 'india';

if (!uid) {
  console.log('Usage: node search-player.js <UID> [region]');
  console.log('Example: node search-player.js 2003919727 india');
  process.exit(1);
}

searchPlayer(uid, region);
