const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// Test function to demonstrate API usage
async function testFreeFireAPI() {
  console.log('🎮 Testing Free Fire Player Info API\n');

  try {
    // Test 1: Get API information
    console.log('1. Testing API Information endpoint...');
    const apiInfo = await axios.get(`${BASE_URL}/`);
    console.log('✅ API Info:', apiInfo.data.message);
    console.log('📋 Available endpoints:', Object.keys(apiInfo.data.endpoints).length);
    console.log('');

    // Test 2: Health check
    console.log('2. Testing Health Check endpoint...');
    const health = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Health Status:', health.data.status);
    console.log('⏱️ Uptime:', Math.round(health.data.uptime), 'seconds');
    console.log('');

    // Test 3: Verify player UID
    console.log('3. Testing Player UID Verification endpoint...');
    const testUID = '123456789';
    const verification = await axios.get(`${BASE_URL}/api/player/${testUID}/verify`);
    console.log('✅ Player UID Verification:');
    console.log('   - Valid:', verification.data.data.isValid);
    console.log('   - Exists:', verification.data.data.exists);
    console.log('   - Player Name:', verification.data.data.playerName);
    console.log('   - Level:', verification.data.data.level);
    console.log('   - Region:', verification.data.data.region);
    console.log('');

    // Test 4: Get player information
    console.log('4. Testing Player Information endpoint...');
    const playerInfo = await axios.get(`${BASE_URL}/api/player/${testUID}`);
    console.log('✅ Player Info Retrieved:');
    console.log('   - UID:', playerInfo.data.data.uid);
    console.log('   - Nickname:', playerInfo.data.data.nickname);
    console.log('   - Level:', playerInfo.data.data.level);
    console.log('   - Region:', playerInfo.data.data.region);
    console.log('   - Verified:', playerInfo.data.data.verified);
    console.log('   - Data Source:', playerInfo.data.data.dataSource);
    console.log('');

    // Test 5: Get player statistics
    console.log('5. Testing Player Statistics endpoint...');
    const playerStats = await axios.get(`${BASE_URL}/api/player/${testUID}/stats`);
    console.log('✅ Player Stats Retrieved:');
    console.log('   - Verified:', playerStats.data.data.verified);
    console.log('   - Data Source:', playerStats.data.data.dataSource);
    if (playerStats.data.data.stats.classicMode.solo) {
      console.log('   - Solo Matches:', playerStats.data.data.stats.classicMode.solo.matches);
      console.log('   - Solo Wins:', playerStats.data.data.stats.classicMode.solo.wins);
      console.log('   - Solo K/D:', playerStats.data.data.stats.classicMode.solo.kd);
    } else {
      console.log('   - Classic Matches:', playerStats.data.data.stats.classicMode.matches);
      console.log('   - Classic Wins:', playerStats.data.data.stats.classicMode.wins);
      console.log('   - Classic K/D:', playerStats.data.data.stats.classicMode.kd);
    }
    console.log('');

    // Test 6: Get player rank
    console.log('6. Testing Player Rank endpoint...');
    const playerRank = await axios.get(`${BASE_URL}/api/player/${testUID}/rank`);
    console.log('✅ Player Rank Retrieved:');
    console.log('   - Current Rank:', playerRank.data.data.currentRank);
    console.log('   - Rank Points:', playerRank.data.data.rankPoints);
    console.log('   - Tier:', playerRank.data.data.tier);
    console.log('   - Season:', playerRank.data.data.season);
    console.log('');

    // Test 7: Test with region parameter
    console.log('7. Testing with region parameter...');
    const playerInfoIndia = await axios.get(`${BASE_URL}/api/player/${testUID}?region=india`);
    console.log('✅ Player Info (India region):', playerInfoIndia.data.data.region);
    console.log('');

    // Test 8: Error handling - Invalid UID
    console.log('8. Testing error handling with invalid UID...');
    try {
      await axios.get(`${BASE_URL}/api/player/123/verify`);
    } catch (error) {
      console.log('✅ Error handling works:', error.response.data.message);
      console.log('   - Status Code:', error.response.status);
    }
    console.log('');

    // Test 9: Error handling - Non-existent endpoint
    console.log('9. Testing 404 error handling...');
    try {
      await axios.get(`${BASE_URL}/api/nonexistent`);
    } catch (error) {
      console.log('✅ 404 handling works:', error.response.data.message);
      console.log('   - Status Code:', error.response.status);
    }
    console.log('');

    console.log('🎉 All tests completed successfully!');
    console.log('🚀 Your Free Fire API is working perfectly!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('   - Status:', error.response.status);
      console.error('   - Data:', error.response.data);
    }
  }
}

// Run the tests
if (require.main === module) {
  testFreeFireAPI();
}

module.exports = { testFreeFireAPI };
