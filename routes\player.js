const express = require('express');
const router = express.Router();
const freeFireService = require('../services/freeFireService');
const { validateUID } = require('../middleware/validation');
const { successResponse, errorResponse } = require('../utils/responseHelper');

/**
 * GET /api/player/:uid/verify
 * Verify if a player UID exists and is valid
 */
router.get('/:uid/verify', validateUID, async (req, res) => {
  try {
    const { uid } = req.params;
    const { region } = req.query; // Optional region parameter

    console.log(`Verifying player UID: ${uid}, Region: ${region || 'default'}`);

    const verification = await freeFireService.verifyPlayerUID(uid, region);

    if (!verification.isValid) {
      return errorResponse(res, verification.error || 'Invalid UID', 400);
    }

    if (!verification.exists) {
      return errorResponse(res, 'Player not found', 404);
    }

    return successResponse(res, verification, 'Player UID verified successfully');
  } catch (error) {
    console.error('Error verifying player UID:', error);

    if (error.message.includes('Invalid UID')) {
      return errorResponse(res, 'Invalid UID format', 400);
    }

    if (error.message.includes('Player not found')) {
      return errorResponse(res, 'Player not found', 404);
    }

    return errorResponse(res, 'Failed to verify player UID', 500);
  }
});

/**
 * GET /api/player/:uid
 * Get basic player information by UID
 */
router.get('/:uid', validateUID, async (req, res) => {
  try {
    const { uid } = req.params;
    const { region } = req.query; // Optional region parameter
    
    console.log(`Fetching player info for UID: ${uid}, Region: ${region || 'default'}`);
    
    const playerInfo = await freeFireService.getPlayerInfo(uid, region);
    
    if (!playerInfo) {
      return errorResponse(res, 'Player not found', 404);
    }
    
    return successResponse(res, playerInfo, 'Player information retrieved successfully');
  } catch (error) {
    console.error('Error fetching player info:', error);
    
    if (error.message.includes('Invalid UID')) {
      return errorResponse(res, 'Invalid UID format', 400);
    }
    
    if (error.message.includes('Player not found')) {
      return errorResponse(res, 'Player not found', 404);
    }
    
    if (error.message.includes('Rate limit')) {
      return errorResponse(res, 'Rate limit exceeded. Please try again later.', 429);
    }
    
    return errorResponse(res, 'Failed to fetch player information', 500);
  }
});

/**
 * GET /api/player/:uid/stats
 * Get detailed player statistics by UID
 */
router.get('/:uid/stats', validateUID, async (req, res) => {
  try {
    const { uid } = req.params;
    const { region } = req.query; // Optional region parameter
    
    console.log(`Fetching player stats for UID: ${uid}, Region: ${region || 'default'}`);
    
    const playerStats = await freeFireService.getPlayerStats(uid, region);
    
    if (!playerStats) {
      return errorResponse(res, 'Player stats not found', 404);
    }
    
    return successResponse(res, playerStats, 'Player statistics retrieved successfully');
  } catch (error) {
    console.error('Error fetching player stats:', error);
    
    if (error.message.includes('Invalid UID')) {
      return errorResponse(res, 'Invalid UID format', 400);
    }
    
    if (error.message.includes('Player not found')) {
      return errorResponse(res, 'Player stats not found', 404);
    }
    
    if (error.message.includes('Rate limit')) {
      return errorResponse(res, 'Rate limit exceeded. Please try again later.', 429);
    }
    
    return errorResponse(res, 'Failed to fetch player statistics', 500);
  }
});

/**
 * GET /api/player/:uid/rank
 * Get player rank information by UID
 */
router.get('/:uid/rank', validateUID, async (req, res) => {
  try {
    const { uid } = req.params;
    const { region, season } = req.query;
    
    console.log(`Fetching player rank for UID: ${uid}, Region: ${region || 'default'}, Season: ${season || 'current'}`);
    
    const playerRank = await freeFireService.getPlayerRank(uid, region, season);
    
    if (!playerRank) {
      return errorResponse(res, 'Player rank not found', 404);
    }
    
    return successResponse(res, playerRank, 'Player rank information retrieved successfully');
  } catch (error) {
    console.error('Error fetching player rank:', error);
    
    if (error.message.includes('Invalid UID')) {
      return errorResponse(res, 'Invalid UID format', 400);
    }
    
    if (error.message.includes('Player not found')) {
      return errorResponse(res, 'Player rank not found', 404);
    }
    
    return errorResponse(res, 'Failed to fetch player rank information', 500);
  }
});

module.exports = router;
