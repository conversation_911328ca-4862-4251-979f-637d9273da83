{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "Free Fire Player Info API - Get player information by UID", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["free-fire", "api", "player-info", "gaming", "uid"], "author": "medic62633", "license": "ISC", "type": "commonjs", "dependencies": {"axios": "^1.9.0", "cheerio": "^1.0.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "joi": "^17.13.3"}, "devDependencies": {"nodemon": "^3.1.10"}}