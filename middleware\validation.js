const Joi = require('joi');
const { errorResponse } = require('../utils/responseHelper');

/**
 * Validate UID parameter
 */
const validateUID = (req, res, next) => {
  const schema = Joi.object({
    uid: Joi.string()
      .pattern(/^\d{9,12}$/)
      .required()
      .messages({
        'string.pattern.base': 'UID must be a 9-12 digit number',
        'any.required': 'UID is required',
        'string.empty': 'UID cannot be empty'
      })
  });

  const { error } = schema.validate({ uid: req.params.uid });
  
  if (error) {
    return errorResponse(res, error.details[0].message, 400);
  }
  
  next();
};

/**
 * Validate query parameters
 */
const validateQuery = (req, res, next) => {
  const schema = Joi.object({
    region: Joi.string()
      .valid('global', 'india', 'brazil', 'indonesia', 'thailand', 'vietnam', 'middle-east')
      .optional()
      .default('global'),
    season: Joi.string()
      .optional()
      .default('current'),
    limit: Joi.number()
      .integer()
      .min(1)
      .max(100)
      .optional()
      .default(10),
    offset: Joi.number()
      .integer()
      .min(0)
      .optional()
      .default(0)
  });

  const { error, value } = schema.validate(req.query);
  
  if (error) {
    return errorResponse(res, error.details[0].message, 400);
  }
  
  // Set validated values back to req.query
  req.query = value;
  next();
};

/**
 * Validate request body for POST/PUT requests
 */
const validateBody = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body);
    
    if (error) {
      return errorResponse(res, error.details[0].message, 400);
    }
    
    req.body = value;
    next();
  };
};

/**
 * Common validation schemas
 */
const schemas = {
  // Player search schema
  playerSearch: Joi.object({
    nickname: Joi.string().min(3).max(20).optional(),
    region: Joi.string().valid('global', 'india', 'brazil', 'indonesia', 'thailand', 'vietnam', 'middle-east').optional(),
    minLevel: Joi.number().integer().min(1).max(100).optional(),
    maxLevel: Joi.number().integer().min(1).max(100).optional()
  }),
  
  // Guild search schema
  guildSearch: Joi.object({
    guildName: Joi.string().min(3).max(30).optional(),
    region: Joi.string().valid('global', 'india', 'brazil', 'indonesia', 'thailand', 'vietnam', 'middle-east').optional(),
    minMembers: Joi.number().integer().min(1).max(50).optional()
  })
};

module.exports = {
  validateUID,
  validateQuery,
  validateBody,
  schemas
};
