<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free Fire Player Info API Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .search-section {
            margin-bottom: 30px;
        }
        
        .input-group {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        input[type="text"], select {
            flex: 1;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        input[type="text"]:focus, select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        button {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
            flex: 1;
            min-width: 120px;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-secondary {
            background: #48cae4;
            color: white;
        }
        
        .btn-tertiary {
            background: #06ffa5;
            color: #333;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .result-section {
            margin-top: 30px;
        }
        
        .result-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }
        
        .result-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }
        
        .result-content {
            background: white;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e0e0e0;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .error {
            color: #e74c3c;
            background: #ffeaea;
            border-left-color: #e74c3c;
        }
        
        .success {
            border-left-color: #27ae60;
        }
        
        .api-info {
            background: #e8f4fd;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 4px solid #3498db;
        }
        
        .endpoint {
            background: #f1f3f4;
            padding: 8px 12px;
            border-radius: 5px;
            font-family: monospace;
            margin: 5px 0;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 Free Fire Player Info API</h1>
            <p>Get player information, statistics, and rank data by UID</p>
        </div>
        
        <div class="content">
            <div class="api-info">
                <h3>📡 API Endpoints</h3>
                <p><span class="endpoint">GET /api/player/:uid/verify</span> - Verify player UID exists</p>
                <p><span class="endpoint">GET /api/player/:uid</span> - Get player basic information</p>
                <p><span class="endpoint">GET /api/player/:uid/stats</span> - Get player detailed statistics</p>
                <p><span class="endpoint">GET /api/player/:uid/rank</span> - Get player rank information</p>
            </div>
            
            <div class="search-section">
                <h3>🔍 Test the API</h3>
                <div class="input-group">
                    <input type="text" id="uidInput" placeholder="Enter Player UID (9-12 digits)" value="123456789">
                    <select id="regionSelect">
                        <option value="global">Global</option>
                        <option value="india">India</option>
                        <option value="brazil">Brazil</option>
                        <option value="indonesia">Indonesia</option>
                        <option value="thailand">Thailand</option>
                        <option value="vietnam">Vietnam</option>
                        <option value="middle-east">Middle East</option>
                    </select>
                </div>
                
                <div class="btn-group">
                    <button class="btn-primary" onclick="verifyPlayerUID()">Verify UID</button>
                    <button class="btn-secondary" onclick="getPlayerInfo()">Get Player Info</button>
                    <button class="btn-tertiary" onclick="getPlayerStats()">Get Player Stats</button>
                    <button class="btn-primary" onclick="getPlayerRank()">Get Player Rank</button>
                </div>
            </div>
            
            <div class="result-section" id="resultSection" style="display: none;">
                <div class="result-card" id="resultCard">
                    <div class="result-title" id="resultTitle">Result</div>
                    <div class="result-content" id="resultContent"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000';
        
        function showResult(title, content, isError = false) {
            const resultSection = document.getElementById('resultSection');
            const resultCard = document.getElementById('resultCard');
            const resultTitle = document.getElementById('resultTitle');
            const resultContent = document.getElementById('resultContent');
            
            resultTitle.textContent = title;
            resultContent.textContent = JSON.stringify(content, null, 2);
            
            resultCard.className = isError ? 'result-card error' : 'result-card success';
            resultSection.style.display = 'block';
            
            resultSection.scrollIntoView({ behavior: 'smooth' });
        }
        
        function showLoading() {
            showResult('Loading...', { message: 'Fetching data from API...' });
        }
        
        async function makeAPICall(endpoint) {
            try {
                showLoading();
                const response = await fetch(endpoint);
                const data = await response.json();

                if (response.ok) {
                    showResult(`✅ Success (${response.status})`, data);
                } else {
                    showResult(`❌ Error (${response.status})`, data, true);
                }
            } catch (error) {
                showResult('❌ Network Error', {
                    error: error.message,
                    message: 'Failed to connect to API. Make sure the server is running on port 3000.'
                }, true);
            }
        }

        function verifyPlayerUID() {
            const uid = document.getElementById('uidInput').value;
            const region = document.getElementById('regionSelect').value;

            if (!uid) {
                showResult('❌ Validation Error', { message: 'Please enter a UID' }, true);
                return;
            }

            const endpoint = `${API_BASE}/api/player/${uid}/verify?region=${region}`;
            makeAPICall(endpoint);
        }
        
        function getPlayerInfo() {
            const uid = document.getElementById('uidInput').value;
            const region = document.getElementById('regionSelect').value;
            
            if (!uid) {
                showResult('❌ Validation Error', { message: 'Please enter a UID' }, true);
                return;
            }
            
            const endpoint = `${API_BASE}/api/player/${uid}?region=${region}`;
            makeAPICall(endpoint);
        }
        
        function getPlayerStats() {
            const uid = document.getElementById('uidInput').value;
            const region = document.getElementById('regionSelect').value;
            
            if (!uid) {
                showResult('❌ Validation Error', { message: 'Please enter a UID' }, true);
                return;
            }
            
            const endpoint = `${API_BASE}/api/player/${uid}/stats?region=${region}`;
            makeAPICall(endpoint);
        }
        
        function getPlayerRank() {
            const uid = document.getElementById('uidInput').value;
            const region = document.getElementById('regionSelect').value;
            
            if (!uid) {
                showResult('❌ Validation Error', { message: 'Please enter a UID' }, true);
                return;
            }
            
            const endpoint = `${API_BASE}/api/player/${uid}/rank?region=${region}&season=current`;
            makeAPICall(endpoint);
        }
        
        // Test with sample data on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                showResult('🎮 Welcome to Free Fire API Demo', {
                    message: 'Enter a UID and click any button to test the API',
                    sampleUID: '123456789',
                    note: 'This demo uses mock data for demonstration purposes'
                });
            }, 500);
        });
    </script>
</body>
</html>
