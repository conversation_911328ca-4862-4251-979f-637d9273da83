/**
 * Global error handling middleware
 */
const errorHandler = (err, req, res, next) => {
  console.error('Error occurred:', {
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString()
  });

  // Default error response
  let statusCode = 500;
  let message = 'Internal Server Error';
  let details = null;

  // Handle specific error types
  if (err.name === 'ValidationError') {
    statusCode = 400;
    message = 'Validation Error';
    details = err.details || err.message;
  } else if (err.name === 'CastError') {
    statusCode = 400;
    message = 'Invalid ID format';
  } else if (err.code === 'ECONNREFUSED') {
    statusCode = 503;
    message = 'Service temporarily unavailable';
  } else if (err.code === 'ENOTFOUND') {
    statusCode = 503;
    message = 'External service not found';
  } else if (err.response) {
    // Axios error
    statusCode = err.response.status || 500;
    message = err.response.data?.message || err.message;
  } else if (err.message) {
    // Custom error messages
    if (err.message.includes('Invalid UID')) {
      statusCode = 400;
      message = 'Invalid UID format';
    } else if (err.message.includes('Player not found')) {
      statusCode = 404;
      message = 'Player not found';
    } else if (err.message.includes('Rate limit')) {
      statusCode = 429;
      message = 'Rate limit exceeded';
    } else if (err.message.includes('Timeout')) {
      statusCode = 408;
      message = 'Request timeout';
    }
  }

  // Don't expose internal errors in production
  if (process.env.NODE_ENV === 'production' && statusCode === 500) {
    message = 'Something went wrong';
    details = null;
  }

  const errorResponse = {
    error: true,
    message: message,
    statusCode: statusCode,
    timestamp: new Date().toISOString(),
    path: req.originalUrl,
    method: req.method
  };

  // Add details if available and not in production
  if (details && process.env.NODE_ENV !== 'production') {
    errorResponse.details = details;
  }

  // Add request ID if available
  if (req.id) {
    errorResponse.requestId = req.id;
  }

  res.status(statusCode).json(errorResponse);
};

/**
 * 404 Not Found handler
 */
const notFoundHandler = (req, res) => {
  res.status(404).json({
    error: true,
    message: 'Endpoint not found',
    statusCode: 404,
    timestamp: new Date().toISOString(),
    path: req.originalUrl,
    method: req.method,
    availableEndpoints: [
      'GET /',
      'GET /health',
      'GET /api/player/:uid',
      'GET /api/player/:uid/stats',
      'GET /api/player/:uid/rank'
    ]
  });
};

/**
 * Async error wrapper
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

module.exports = {
  errorHandler,
  notFoundHandler,
  asyncHandler
};
